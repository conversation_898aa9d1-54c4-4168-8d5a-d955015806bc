from flask import Flask, render_template, request, jsonify
import os
import base64
from io import BytesIO
from PIL import Image
import torch
from ultralytics import YOLO
import numpy as np

app = Flask(__name__)

# 配置上传文件夹
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 加载模型
model_path = 'best.pt'
model = None

def load_model():
    global model
    try:
        model = YOLO(model_path)
        print("模型加载成功！")
        print(f"类别: {model.names}")
        return True
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        return False

def predict_image(image_path):
    """使用模型预测图像"""
    try:
        # 进行预测
        results = model(image_path, verbose=False)
        probs = results[0].probs
        
        # 获取Top5预测结果
        top5_indices = probs.top5
        top5_scores = probs.top5conf.tolist()
        
        # 获取类别名称
        class_names = [model.names[i] for i in range(len(model.names))]
        top5_names = [class_names[i] for i in top5_indices]
        
        # 组织结果
        predictions = []
        for i, (name, score) in enumerate(zip(top5_names, top5_scores)):
            predictions.append({
                'rank': i + 1,
                'class_name': name,
                'confidence': round(score * 100, 2)  # 转换为百分比
            })
        
        return predictions
    except Exception as e:
        print(f"预测失败: {str(e)}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    if 'file' not in request.files:
        return jsonify({'error': '没有上传文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'})
    
    if file:
        try:
            # 保存上传的文件
            filename = f"temp_image.{file.filename.split('.')[-1]}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # 进行预测
            predictions = predict_image(filepath)
            
            if predictions is None:
                return jsonify({'error': '预测失败，请检查图像格式'})
            
            # 将图像转换为base64以便在前端显示
            with open(filepath, 'rb') as img_file:
                img_data = base64.b64encode(img_file.read()).decode()
            
            # 删除临时文件
            os.remove(filepath)
            
            return jsonify({
                'success': True,
                'image_data': img_data,
                'predictions': predictions
            })
            
        except Exception as e:
            return jsonify({'error': f'处理图像时出错: {str(e)}'})

if __name__ == '__main__':
    print("🦐 海鲜识别Web应用启动中...")
    print("检查模型文件...")
    
    if not os.path.exists(model_path):
        print(f"❌ 错误：找不到模型文件 {model_path}")
        print("请确保best.pt文件在当前目录下")
        input("按任意键退出...")
        exit(1)
    
    if load_model():
        print("✅ 模型加载成功！")
        print("🌐 启动Web服务器...")
        print("📱 访问地址: http://localhost:5000")
        print("📱 或访问: http://127.0.0.1:5000")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("-" * 50)
        try:
            app.run(debug=False, host='0.0.0.0', port=5000)
        except KeyboardInterrupt:
            print("\n👋 服务器已停止")
        except Exception as e:
            print(f"❌ 服务器启动失败: {str(e)}")
    else:
        print("❌ 无法启动应用，模型加载失败！")
        print("请检查:")
        print("1. best.pt文件是否存在")
        print("2. ultralytics是否正确安装")
        print("3. PyTorch是否正确安装")
        input("按任意键退出...")
        exit(1)
