# 海鲜分类识别Web应用

基于YOLO11L-CLS模型的海鲜分类识别系统，提供简洁美观的Web界面进行图像上传和预测。

## 功能特点

- 🎯 **智能识别**: 使用训练好的YOLO11L-CLS模型识别海鲜类别
- 📊 **Top5预测**: 显示置信度最高的前5个预测结果
- 🖼️ **图片预览**: 实时预览上传的图片
- 📱 **响应式设计**: 支持手机、平板、电脑等各种设备
- 🎨 **美观界面**: 现代化的渐变UI设计
- 🚀 **快速部署**: 一键启动，即开即用

## 安装和运行

### 方法一：使用启动脚本（推荐）

1. 确保已安装Python 3.7+
2. 双击运行 `start.bat` 文件
3. 等待依赖安装完成
4. 浏览器访问 http://localhost:5000

### 方法二：手动安装

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行应用：
```bash
python app.py
```

3. 打开浏览器，访问 http://localhost:5000

## 使用说明

1. **上传图片**: 
   - 点击上传区域选择图片文件
   - 或者直接拖拽图片到上传区域
   - 支持JPG、PNG、JPEG格式，最大16MB

2. **查看结果**:
   - 系统会显示上传的图片预览
   - 展示Top5预测结果，包含：
     - 排名（金色标识第一名）
     - 海鲜类别名称
     - 置信度百分比
     - 可视化置信度条

## 文件结构

```
seafood_recognition/
├── app.py              # Flask主应用
├── best.pt             # 训练好的YOLO模型
├── requirements.txt    # Python依赖包
├── start.bat          # Windows启动脚本
├── README.md          # 说明文档
└── templates/
    └── index.html     # Web界面模板
```

## 技术栈

- **后端**: Flask (Python Web框架)
- **AI模型**: YOLOv11 Classification
- **前端**: HTML5 + CSS3 + JavaScript
- **深度学习**: PyTorch + Ultralytics

## 注意事项

- 确保 `best.pt` 模型文件在项目根目录
- 首次运行需要联网下载依赖包
- 建议使用现代浏览器（Chrome、Firefox、Edge等）
- 如果模型加载失败，请检查模型文件路径

## 故障排除

1. **模型加载失败**: 检查best.pt文件是否存在且完整
2. **端口占用**: 修改app.py中的端口号（默认5000）
3. **依赖安装失败**: 尝试升级pip：`pip install --upgrade pip`

## 联系信息

如有问题，请检查终端输出的错误信息，或查看Flask应用日志。
