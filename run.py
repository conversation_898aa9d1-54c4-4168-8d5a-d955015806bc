#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("正在启动海鲜识别Web应用...")
print("请确保已安装以下依赖包:")
print("- flask")
print("- ultralytics") 
print("- pillow")
print("- torch")
print("- numpy")
print("- opencv-python")

try:
    from flask import Flask
    print("✓ Flask 已安装")
except ImportError:
    print("✗ 请安装 Flask: pip install flask")
    exit(1)

try:
    from ultralytics import YOLO
    print("✓ Ultralytics 已安装")
except ImportError:
    print("✗ 请安装 Ultralytics: pip install ultralytics")
    exit(1)

try:
    from PIL import Image
    print("✓ Pillow 已安装")
except ImportError:
    print("✗ 请安装 Pillow: pip install pillow")
    exit(1)

# 导入主应用
from app import app, load_model

if __name__ == '__main__':
    print("\n正在加载模型...")
    if load_model():
        print("✓ 模型加载成功！")
        print("\n🚀 启动Web服务器...")
        print("📱 访问地址: http://localhost:5000")
        print("📱 或访问: http://127.0.0.1:5000")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("-" * 50)
        app.run(debug=False, host='0.0.0.0', port=5000)
    else:
        print("✗ 模型加载失败！请检查 best.pt 文件是否存在。")
