#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
海鲜识别Web应用演示脚本
快速测试模型加载和预测功能
"""

import os
import sys

def test_dependencies():
    """测试依赖包是否正确安装"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        ('flask', 'Flask'),
        ('ultralytics', 'YOLO'),
        ('PIL', 'Image'),
        ('torch', 'torch'),
        ('numpy', 'np')
    ]
    
    missing_packages = []
    
    for package, import_name in required_packages:
        try:
            if package == 'PIL':
                from PIL import Image
            elif package == 'ultralytics':
                from ultralytics import YOLO
            elif package == 'flask':
                import flask
            elif package == 'torch':
                import torch
            elif package == 'numpy':
                import numpy as np
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖包检查完成！")
    return True

def test_model():
    """测试模型文件"""
    print("\n🔍 检查模型文件...")
    
    model_path = 'best.pt'
    if not os.path.exists(model_path):
        print(f"❌ 模型文件 {model_path} 不存在！")
        return False
    
    try:
        from ultralytics import YOLO
        model = YOLO(model_path)
        print(f"✅ 模型加载成功！")
        print(f"📋 类别数量: {len(model.names)}")
        print(f"📋 类别列表: {list(model.names.values())}")
        return True
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return False

def start_app():
    """启动Web应用"""
    print("\n🚀 启动海鲜识别Web应用...")
    print("="*50)
    
    try:
        from app import app, load_model
        
        if load_model():
            print("✅ 模型加载成功！")
            print("\n🌐 Web服务器信息:")
            print("   地址: http://localhost:5000")
            print("   备用地址: http://127.0.0.1:5000")
            print("\n💡 使用说明:")
            print("   1. 在浏览器中打开上述地址")
            print("   2. 上传海鲜图片")
            print("   3. 查看AI识别结果")
            print("\n⏹️  按 Ctrl+C 停止服务器")
            print("="*50)
            
            app.run(debug=False, host='0.0.0.0', port=5000)
        else:
            print("❌ 模型加载失败，无法启动应用！")
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🦐 海鲜识别Web应用")
    print("="*30)
    
    # 检查依赖
    if not test_dependencies():
        print("\n❌ 请先安装缺少的依赖包！")
        return
    
    # 检查模型
    if not test_model():
        print("\n❌ 请检查模型文件！")
        return
    
    # 启动应用
    start_app()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止，感谢使用！")
