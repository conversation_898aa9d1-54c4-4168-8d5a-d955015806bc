<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海鲜分类识别系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .upload-area:hover {
            background: #eff1ff;
            border-color: #764ba2;
        }

        .upload-area.dragover {
            background: #e8ecff;
            border-color: #5a67d8;
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.3em;
            color: #667eea;
            margin-bottom: 10px;
        }        .upload-subtext {
            color: #666;
            font-size: 0.9em;
        }

        .paste-hint {
            color: #667eea;
            font-size: 0.85em;
            margin-top: 10px;
            font-style: italic;
            opacity: 0.8;
        }

        #fileInput {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102,126,234,0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.4);
        }        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .shortcuts-info {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .shortcut-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            color: #666;
        }

        .shortcut-key {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8em;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
        }        .shortcut-desc {
            font-size: 0.85em;
        }

        .status-indicator {
            margin-top: 15px;
            text-align: center;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .status-text {
            font-size: 0.9em;
            color: #667eea;
            font-weight: 500;
        }

        .results-section {
            display: none;
            margin-top: 40px;
        }

        .image-preview {
            text-align: center;
            margin-bottom: 30px;
        }

        .preview-img {
            max-width: 400px;
            max-height: 400px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .predictions {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
        }

        .predictions h3 {
            color: #667eea;
            margin-bottom: 25px;
            font-size: 1.5em;
            text-align: center;
        }

        .prediction-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .prediction-item:hover {
            transform: translateX(5px);
        }

        .rank {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .rank.top1 {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
        }

        .class-name {
            flex: 1;
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }

        .confidence {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
        }

        .confidence-bar {
            width: 100px;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 5px 0 0 10px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fee;
            color: #c53030;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #e53e3e;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .upload-area {
                padding: 40px 15px;
            }
            
            .preview-img {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦐 海鲜分类识别系统</h1>
            <p>基于 YOLO11L-CLS 的智能海鲜识别</p>
        </div>
        
        <div class="content">
            <div class="upload-section">                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">点击、拖拽或粘贴上传海鲜图片</div>
                    <div class="upload-subtext">支持 JPG、PNG、JPEG 格式，最大 16MB</div>
                    <div class="paste-hint">💡 提示：可直接按 Ctrl+V 粘贴剪贴板中的图片</div>                </div>
                <input type="file" id="fileInput" accept="image/*">
                <button class="btn" id="uploadBtn">选择图片</button>
                  <div class="shortcuts-info">
                    <div class="shortcut-item">
                        <span class="shortcut-key">Ctrl+V</span>
                        <span class="shortcut-desc">粘贴剪贴板图片</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-key">拖拽</span>
                        <span class="shortcut-desc">拖拽图片到上传区域</span>
                    </div>
                </div>
                
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-text">🎯 就绪：等待图片上传</span>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>AI 正在识别中，请稍候...</p>
            </div>
            
            <div class="results-section" id="resultsSection">
                <div class="image-preview">
                    <img id="previewImg" class="preview-img" alt="预览图片">
                </div>
                
                <div class="predictions">
                    <h3>🎯 识别结果 - Top 5 预测</h3>
                    <div id="predictionsList"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');        const uploadBtn = document.getElementById('uploadBtn');
        const loading = document.getElementById('loading');
        const resultsSection = document.getElementById('resultsSection');
        const previewImg = document.getElementById('previewImg');
        const predictionsList = document.getElementById('predictionsList');
        const statusIndicator = document.getElementById('statusIndicator');

        // 更新状态指示器
        function updateStatus(message, type = 'info') {
            const statusText = statusIndicator.querySelector('.status-text');
            const icons = {
                'ready': '🎯',
                'uploading': '📤',
                'processing': '🤖',
                'success': '✅',
                'error': '❌'
            };
            
            const colors = {
                'ready': '#667eea',
                'uploading': '#f6ad55',
                'processing': '#667eea',
                'success': '#48bb78',
                'error': '#f56565'
            };
            
            const icon = icons[type] || '🎯';
            const color = colors[type] || '#667eea';
            
            statusText.textContent = `${icon} ${message}`;
            statusText.style.color = color;
        }

        // 点击上传按钮
        uploadBtn.addEventListener('click', () => {
            fileInput.click();
        });

        // 点击上传区域
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });        // 拖拽功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
            updateStatus('松开鼠标来上传图片', 'uploading');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
            updateStatus('就绪：等待图片上传', 'ready');
        });        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                updateStatus('图片已接收，准备处理...', 'uploading');
                handleFile(files[0]);
            }
        });        // 粘贴功能 - 兼容HTTP和HTTPS环境
        document.addEventListener('paste', async (e) => {
            // 如果用户正在输入框中，不拦截粘贴
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }
            
            e.preventDefault();
            updateStatus('正在检测剪贴板内容...', 'processing');
            
            let hasImage = false;
            
            // 方法1: 使用传统的clipboardData (HTTP环境兼容)
            if (e.clipboardData && e.clipboardData.items) {
                const items = e.clipboardData.items;
                console.log('粘贴事件触发，剪贴板项目数量:', items.length);
                
                // 优先处理图片文件
                for (let i = 0; i < items.length; i++) {
                    const item = items[i];
                    console.log('剪贴板项目类型:', item.type, '种类:', item.kind);
                    
                    if (item.type.indexOf('image') !== -1) {
                        const file = item.getAsFile();
                        if (file) {
                            hasImage = true;
                            console.log('检测到粘贴的图片:', {
                                name: file.name || 'clipboard-image.png',
                                size: file.size,
                                type: file.type
                            });
                            
                            showPasteSuccess();
                            handleFile(file);
                            return;
                        }
                    }
                }
                
                // 检查文件类型内容
                for (let i = 0; i < items.length; i++) {
                    const item = items[i];
                    if (item.kind === 'file') {
                        const file = item.getAsFile();
                        if (file && file.type.startsWith('image/')) {
                            hasImage = true;
                            showPasteSuccess();
                            handleFile(file);
                            return;
                        }
                    }
                }
            }
            
            // 方法2: 尝试使用现代Clipboard API (HTTPS环境)
            if (!hasImage && navigator.clipboard && navigator.clipboard.read) {
                try {
                    const clipboardItems = await navigator.clipboard.read();
                    for (const clipboardItem of clipboardItems) {
                        for (const type of clipboardItem.types) {
                            if (type.startsWith('image/')) {
                                const blob = await clipboardItem.getType(type);
                                const file = new File([blob], 'clipboard-image.png', { type: blob.type });
                                hasImage = true;
                                showPasteSuccess();
                                handleFile(file);
                                return;
                            }
                        }
                    }
                } catch (clipboardError) {
                    console.log('现代剪贴板API不可用:', clipboardError.message);
                }
            }
            
            // 如果没有找到图片，显示提示
            if (!hasImage) {
                showPasteError();
            }
        });

        // 显示粘贴成功提示
        function showPasteSuccess() {
            // 移除之前的提示
            const existingNotice = document.querySelector('.paste-success');
            if (existingNotice) {
                existingNotice.remove();
            }
            
            const notice = document.createElement('div');
            notice.className = 'paste-success';
            notice.innerHTML = '✅ 图片粘贴成功！正在处理...';
            notice.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
                font-size: 0.9em;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(notice);
            
            // 3秒后自动移除
            setTimeout(() => {
                notice.remove();            }, 3000);
        }        // 显示粘贴错误提示
        function showPasteError() {
            // 移除之前的提示
            const existingNotice = document.querySelector('.paste-error');
            if (existingNotice) {
                existingNotice.remove();
            }
            
            const notice = document.createElement('div');
            notice.className = 'paste-error';
            
            // 根据环境显示不同的错误信息
            const isHTTPS = location.protocol === 'https:';
            const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            
            let errorMessage = '⚠️ 剪贴板中没有图片，请先复制图片再粘贴';
            
            if (!isHTTPS && !isLocalhost) {
                errorMessage = '⚠️ HTTP环境下粘贴功能受限，请尝试：<br/>1. 使用文件选择或拖拽上传<br/>2. 或访问HTTPS版本以使用完整粘贴功能';
            }
            
            notice.innerHTML = errorMessage;
            notice.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
                font-size: 0.9em;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                max-width: 350px;
                line-height: 1.4;
            `;
            
            document.body.appendChild(notice);
            
            // 错误信息显示时间稍长
            setTimeout(() => {
                notice.remove();
            }, 5000);
        }

        // 添加滑入动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }        `;
        document.head.appendChild(style);        // 键盘快捷键监听
        document.addEventListener('keydown', (e) => {
            // 监听 Ctrl+V
            if (e.ctrlKey && e.key === 'v') {
                updateStatus('正在检测剪贴板内容...', 'processing');
                
                // 高亮上传区域，提示用户正在尝试粘贴
                uploadArea.style.transform = 'scale(1.02)';
                uploadArea.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.5)';
                
                setTimeout(() => {
                    uploadArea.style.transform = '';
                    uploadArea.style.boxShadow = '';
                }, 300);
            }
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });        function handleFile(file) {
            updateStatus('正在验证文件...', 'processing');
            
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                updateStatus('文件类型错误', 'error');
                showError('请选择图片文件！');
                setTimeout(() => updateStatus('就绪：等待图片上传', 'ready'), 2000);
                return;
            }

            // 验证文件大小 (16MB)
            if (file.size > 16 * 1024 * 1024) {
                updateStatus('文件过大', 'error');
                showError('文件大小不能超过 16MB！');
                setTimeout(() => updateStatus('就绪：等待图片上传', 'ready'), 2000);
                return;
            }

            updateStatus('文件验证通过，开始上传...', 'uploading');
            uploadImage(file);
        }function uploadImage(file) {
            const formData = new FormData();
            formData.append('file', file);

            // 显示加载状态
            updateStatus('AI正在识别海鲜类别...', 'processing');
            loading.style.display = 'block';
            resultsSection.style.display = 'none';
            uploadBtn.disabled = true;

            fetch('/predict', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                uploadBtn.disabled = false;

                if (data.error) {
                    updateStatus('识别失败：' + data.error, 'error');
                    showError(data.error);
                } else {
                    updateStatus('识别完成！查看下方结果', 'success');
                    showResults(data);
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                uploadBtn.disabled = false;
                updateStatus('网络错误，请重试', 'error');
                showError('网络错误，请重试！');
                console.error('Error:', error);
            });
        }

        function showResults(data) {
            // 显示预览图片
            previewImg.src = 'data:image/jpeg;base64,' + data.image_data;
            
            // 清空之前的结果
            predictionsList.innerHTML = '';
            
            // 显示预测结果
            data.predictions.forEach(pred => {
                const predItem = document.createElement('div');
                predItem.className = 'prediction-item';
                
                const rankClass = pred.rank === 1 ? 'rank top1' : 'rank';
                
                predItem.innerHTML = `
                    <div class="${rankClass}">${pred.rank}</div>
                    <div class="class-name">${pred.class_name}</div>
                    <div class="confidence">${pred.confidence}%</div>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${pred.confidence}%"></div>
                    </div>
                `;
                
                predictionsList.appendChild(predItem);
            });
            
            resultsSection.style.display = 'block';
            
            // 滚动到结果区域
            resultsSection.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }        function showError(message) {
            // 移除之前的错误信息
            const existingError = document.querySelector('.error');
            if (existingError) {
                existingError.remove();
            }
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            
            const uploadSection = document.querySelector('.upload-section');
            uploadSection.appendChild(errorDiv);
            
            // 3秒后自动移除错误信息
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        }

        // 检测剪贴板支持状态
        function checkClipboardSupport() {
            const isHTTPS = location.protocol === 'https:';
            const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            const hasClipboardAPI = navigator.clipboard && navigator.clipboard.read;
            const hasBasicPaste = true; // 基本粘贴事件始终支持
            
            console.log('剪贴板环境检测:', {
                protocol: location.protocol,
                hostname: location.hostname,
                isHTTPS,
                isLocalhost,
                hasClipboardAPI,
                hasBasicPaste
            });
            
            // 更新状态提示
            let statusMessage = '🎯 就绪：等待图片上传';
            let pasteHint = '💡 提示：可直接按 Ctrl+V 粘贴剪贴板中的图片';
            
            if (!isHTTPS && !isLocalhost) {
                pasteHint = '⚠️ 提示：HTTP环境下粘贴功能可能受限，建议使用HTTPS';
            } else if (hasClipboardAPI) {
                pasteHint = '✅ 提示：完全支持剪贴板功能，可直接按 Ctrl+V 粘贴图片';
            }
            
            // 更新页面提示文本
            const pasteHintElement = document.querySelector('.paste-hint');
            if (pasteHintElement) {
                pasteHintElement.textContent = pasteHint;
            }
            
            updateStatus(statusMessage, 'ready');
        }

        // 页面加载完成后检测环境
        window.addEventListener('load', () => {
            checkClipboardSupport();
        });
    </script>
</body>
</html>
