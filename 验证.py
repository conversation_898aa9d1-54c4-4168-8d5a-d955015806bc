import os
import sys

def main():
    print("🦐 海鲜识别Web应用 - 快速验证")
    print("=" * 40)
    
    # 检查关键文件
    files_to_check = [
        ('best.pt', '模型文件'),
        ('app.py', '主应用'),
        ('templates/index.html', '网页模板'),
        ('start.bat', '启动脚本')
    ]
    
    print("📂 检查文件...")
    all_files_exist = True
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {description}: {file_path}")
        else:
            print(f"❌ {description}: {file_path} - 文件缺失")
            all_files_exist = False
    
    if not all_files_exist:
        print("\n❌ 部分文件缺失，请检查文件完整性")
        return False
    
    # 检查关键依赖
    print(f"\n📦 检查Python依赖...")
    required_packages = ['flask', 'ultralytics', 'PIL', 'torch']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                print(f"✅ Pillow")
            elif package == 'ultralytics':
                import ultralytics
                print(f"✅ Ultralytics")
            elif package == 'flask':
                import flask
                print(f"✅ Flask")
            elif package == 'torch':
                import torch
                print(f"✅ PyTorch")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包，请运行:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print(f"\n🤖 测试模型加载...")
    try:
        from ultralytics import YOLO
        model = YOLO('best.pt')
        class_names = list(model.names.values())
        print(f"✅ 模型加载成功")
        print(f"📊 识别类别数: {len(class_names)}")
        print(f"📋 类别列表: {class_names[:5]}{'...' if len(class_names) > 5 else ''}")
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return False
    
    print(f"\n🎉 所有检查通过！")
    print(f"\n🚀 启动应用:")
    print(f"   方法1: 双击 start.bat")
    print(f"   方法2: 运行 python app.py")
    print(f"\n🌐 访问地址:")
    print(f"   http://localhost:5000")
    print(f"   http://127.0.0.1:5000")
    
    return True

if __name__ == '__main__':
    success = main()
    print(f"\n{'='*40}")
    if success:
        print("✅ 系统就绪，可以启动应用！")
    else:
        print("❌ 请解决上述问题后重试")
    
    input("\n按回车键退出...")
