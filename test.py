"""
快速测试脚本 - 验证海鲜识别Web应用设置
"""

print("🦐 海鲜识别系统测试")
print("=" * 40)

# 1. 检查文件
import os
print("📂 检查文件结构...")

required_files = [
    'best.pt',
    'app.py', 
    'templates/index.html',
    'uploads'
]

for file in required_files:
    if os.path.exists(file):
        print(f"✅ {file}")
    else:
        print(f"❌ {file} - 缺失")

print("\n📦 检查Python包...")

# 2. 检查包
packages = {
    'flask': 'Flask',
    'ultralytics': 'YOLO',
    'PIL': 'Image',
    'torch': 'torch'
}

for pkg_name, import_name in packages.items():
    try:
        if pkg_name == 'flask':
            import flask
            print(f"✅ Flask - 版本 {flask.__version__}")
        elif pkg_name == 'ultralytics':
            from ultralytics import YOLO
            print("✅ Ultralytics")
        elif pkg_name == 'PIL':
            from PIL import Image
            print("✅ Pillow")
        elif pkg_name == 'torch':
            import torch
            print(f"✅ PyTorch - 版本 {torch.__version__}")
    except ImportError as e:
        print(f"❌ {pkg_name} - 未安装")

print("\n🤖 测试模型...")

# 3. 测试模型
try:
    from ultralytics import YOLO
    model = YOLO('best.pt')
    print("✅ 模型加载成功")
    print(f"📊 类别数: {len(model.names)}")
    print(f"📋 类别: {list(model.names.values())}")
except Exception as e:
    print(f"❌ 模型加载失败: {e}")

print("\n🌐 准备启动Web服务...")
print("运行以下命令启动应用:")
print("   python app.py")
print("\n然后在浏览器中访问:")
print("   http://localhost:5000")

input("\n按回车键退出...")
