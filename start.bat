@echo off
echo.
echo ========================================
echo    海鲜识别Web应用启动脚本
echo ========================================
echo.

echo [1/3] 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo.
echo [2/3] 安装依赖包...
pip install flask ultralytics pillow torch torchvision
if errorlevel 1 (
    echo 警告: 部分依赖包安装可能失败
)

echo.
echo [3/3] 启动Web应用...
echo.
echo 应用将在以下地址运行:
echo   http://localhost:5000
echo   http://127.0.0.1:5000
echo.
echo 按 Ctrl+C 停止应用
echo ========================================
echo.

python app.py

echo.
echo 应用已停止
pause
