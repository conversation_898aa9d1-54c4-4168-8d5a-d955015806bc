# -*- coding: utf-8 -*-
"""
快速测试海鲜识别系统
"""
import os
import subprocess
import sys
import time
import webbrowser
from threading import Timer

def check_port(port=5000):
    """检查端口是否被占用"""
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('127.0.0.1', port))
    sock.close()
    return result == 0

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    if check_port(5000):
        print("🌐 自动打开浏览器...")
        webbrowser.open('http://localhost:5000')
    else:
        print("❌ 服务器启动失败")

def main():
    print("🦐 海鲜识别系统 - 快速测试")
    print("=" * 40)
    
    # 检查是否在正确目录
    if not os.path.exists('app.py'):
        print("❌ 错误：请在项目根目录运行此脚本")
        input("按任意键退出...")
        return
    
    # 检查模型文件
    if not os.path.exists('best.pt'):
        print("❌ 错误：找不到模型文件 best.pt")
        input("按任意键退出...")
        return
    
    print("✅ 文件检查通过")
    print("🚀 正在启动Flask服务器...")
    print("📝 测试说明：")
    print("   1. 浏览器将自动打开到 http://localhost:5000")
    print("   2. 测试三种上传方式：")
    print("      - 点击选择文件")
    print("      - 拖拽图片到上传区域") 
    print("      - 复制图片后按 Ctrl+V 粘贴")
    print("   3. 按 Ctrl+C 停止服务器")
    print("-" * 40)
    
    # 设置定时器打开浏览器
    timer = Timer(2.0, open_browser)
    timer.start()
    
    try:
        # 启动Flask应用
        result = subprocess.run([sys.executable, 'app.py'], 
                              cwd=os.getcwd(), 
                              capture_output=False)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        input("按任意键退出...")

if __name__ == "__main__":
    main()
