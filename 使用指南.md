# 🦐 海鲜识别Web应用使用指南

## 🚀 快速开始

### 方法1: 双击启动（推荐）
1. 双击 `start.bat` 文件
2. 等待依赖安装和应用启动
3. 在浏览器中访问 http://localhost:5000

### 方法2: 命令行启动
```bash
# 安装依赖
pip install flask ultralytics pillow torch

# 启动应用
python app.py
```

## 📋 系统要求

- ✅ Python 3.7 或更高版本
- ✅ Windows 操作系统
- ✅ 至少 2GB 可用内存
- ✅ 网络连接（首次安装依赖时需要）

## 🎯 功能特色

### 🖼️ 图片上传
- **支持格式**: JPG, JPEG, PNG
- **文件大小**: 最大 16MB
- **上传方式**: 
  - 点击上传区域选择文件
  - 拖拽图片到上传区域

### 🤖 AI识别
- **模型**: YOLO11L-CLS (海鲜分类专用)
- **识别速度**: 通常 < 3秒
- **输出结果**: Top5 预测 + 置信度

### 📊 结果展示
- 🥇 **排名显示**: 金标Top1，数字标识其他排名
- 📈 **置信度**: 百分比 + 可视化进度条
- 🖼️ **图片预览**: 实时显示上传的图片

## 💡 使用技巧

### 📸 拍照建议
1. **光线充足**: 避免阴影和反光
2. **角度适当**: 正面或45度角拍摄
3. **背景简洁**: 避免复杂背景干扰
4. **焦点清晰**: 确保海鲜主体清晰可见

### 🎯 最佳效果
- 单个海鲜品种的特写
- 完整的海鲜形态
- 自然的颜色和纹理
- 避免过度处理的图片

## 🔧 故障排除

### ❌ 常见问题

**1. 模型加载失败**
- 检查 `best.pt` 文件是否存在
- 确认文件完整性（不是0字节）
- 重新下载模型文件

**2. 端口占用**
```python
# 在 app.py 中修改端口
app.run(debug=False, host='0.0.0.0', port=5001)  # 改为5001
```

**3. 依赖安装失败**
```bash
# 升级pip
pip install --upgrade pip

# 手动安装
pip install flask
pip install ultralytics  
pip install pillow
pip install torch
```

**4. 浏览器无法访问**
- 检查防火墙设置
- 尝试使用 127.0.0.1:5000
- 清除浏览器缓存

### 📞 获取帮助

如遇到问题，请查看终端输出的错误信息：

1. **模型相关错误**: 检查best.pt文件
2. **网络相关错误**: 检查端口和防火墙
3. **依赖相关错误**: 重新安装Python包

## 📁 文件说明

```
seafood_recognition/
├── 📄 app.py              # Flask主应用（核心代码）
├── 🤖 best.pt             # 训练好的AI模型
├── 🚀 start.bat           # Windows启动脚本
├── 📋 requirements.txt    # Python依赖列表
├── 📖 README.md          # 详细文档
├── 🧪 test.py            # 系统测试脚本
├── 📂 templates/         # 网页模板
│   └── 🌐 index.html     # 主页面
└── 📂 uploads/           # 临时文件存储
```

## 🎨 界面预览

### 主页面
- 🎨 **现代渐变设计**: 蓝紫色渐变背景
- 📱 **响应式布局**: 支持手机、平板、电脑
- 🖱️ **友好交互**: 拖拽上传 + 点击上传

### 结果页面
- 🖼️ **图片预览**: 清晰显示上传的图片
- 🏆 **排名展示**: 金色Top1 + 彩色排名标识
- 📊 **置信度条**: 可视化置信度百分比

## 🔮 技术架构

- **前端**: HTML5 + CSS3 + JavaScript
- **后端**: Flask (Python Web框架)
- **AI模型**: YOLOv11 Classification
- **深度学习**: PyTorch + Ultralytics

## 📈 性能优化

- ✅ **混合精度**: 加速推理过程
- ✅ **图片预处理**: 自动调整尺寸
- ✅ **内存管理**: 及时清理临时文件
- ✅ **错误处理**: 完善的异常捕获

---

🎉 **享受使用海鲜识别系统！** 如有问题，请查看终端输出或重新启动应用。
